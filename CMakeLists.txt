
# (Automatically converted from project Makefile by convert_to_cmake.py.)

# The following lines of boilerplate have to be in your project's CMakeLists
# in this exact order for cmake to work correctly
cmake_minimum_required(VERSION 3.5)

set(PROJECT_VER "*******")

# 获取Git信息用于自定义固件描述
execute_process(
    COMMAND git rev-parse --short HEAD
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    OUTPUT_VARIABLE GIT_COMMIT_HASH
    OUTPUT_STRIP_TRAILING_WHITESPACE
    ERROR_QUIET
)

execute_process(
    COMMAND git rev-parse --abbrev-ref HEAD
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    OUTPUT_VARIABLE GIT_BRANCH
    OUTPUT_STRIP_TRAILING_WHITESPACE
    ERROR_QUIET
)

# 生成构建编号（基于时间戳）
string(TIMESTAMP BUILD_TIMESTAMP "%s" UTC)
math(EXPR BUILD_NUMBER "${BUILD_TIMESTAMP} % 100000")

# 设置默认值（如果Git命令失败）
if(NOT GIT_COMMIT_HASH)
    set(GIT_COMMIT_HASH "unknown")
endif()

if(NOT GIT_BRANCH)
    set(GIT_BRANCH "main")
endif()

# 添加编译定义
add_compile_definitions(
    GIT_COMMIT_HASH="${GIT_COMMIT_HASH}"
    GIT_BRANCH="${GIT_BRANCH}"
    BUILD_TIMESTAMP=${BUILD_TIMESTAMP}
    BUILD_NUMBER=${BUILD_NUMBER}
)

# Add this line to disable the specific warning
add_compile_options(-Wno-missing-field-initializers)

add_definitions(-DLV_CONF_INCLUDE_SIMPLE)
add_definitions(-DLV_LVGL_H_INCLUDE_SIMPLE)

include($ENV{ADF_PATH}/CMakeLists.txt)
include($ENV{IDF_PATH}/tools/cmake/project.cmake)
project(DDClock)

idf_build_set_property(COMPILE_OPTIONS "-Wno-error=format= -Wno-format" APPEND)
