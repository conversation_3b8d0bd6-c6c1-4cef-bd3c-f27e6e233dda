#ifndef _CUSTOM_APP_DESC_H
#define _CUSTOM_APP_DESC_H

#include <stdint.h>
#include <stdbool.h>
#include "esp_app_format.h"

#ifdef __cplusplus
extern "C" {
#endif

// 自定义固件描述结构的魔数
#define CUSTOM_APP_DESC_MAGIC 0x44444321  // "DDC!" in little endian

// 支持的验证算法
typedef enum {
    CUSTOM_VERIFY_NONE = 0,
    CUSTOM_VERIFY_CRC32 = 1,
    CUSTOM_VERIFY_SHA256 = 2,
    CUSTOM_VERIFY_RSA_SIGNATURE = 3
} custom_verify_type_t;

// 自定义固件描述结构
typedef struct {
    uint32_t magic;                     // 魔数，用于识别自定义结构
    uint32_t version;                   // 结构版本号
    uint32_t build_timestamp;           // 构建时间戳
    uint32_t build_number;              // 构建编号
    char hardware_version[16];          // 硬件版本要求
    char build_branch[32];              // Git分支名称
    char build_commit[41];              // Git提交哈希（40字符+null）
    uint32_t firmware_size;             // 固件大小
    uint32_t verify_type;               // 验证类型
    uint8_t verify_data[64];            // 验证数据（CRC32/SHA256/签名等）
    uint32_t feature_flags;             // 功能标志位
    uint32_t min_bootloader_version;    // 最小bootloader版本要求
    uint8_t reserved[32];               // 保留字段，用于未来扩展
    uint32_t crc32;                     // 整个结构的CRC32校验
} __attribute__((packed)) custom_app_desc_t;

// 功能标志位定义
#define CUSTOM_FEATURE_WIFI_REQUIRED    (1 << 0)
#define CUSTOM_FEATURE_BLE_REQUIRED     (1 << 1)
#define CUSTOM_FEATURE_AUDIO_REQUIRED   (1 << 2)
#define CUSTOM_FEATURE_DISPLAY_REQUIRED (1 << 3)

// 函数声明
const custom_app_desc_t* custom_app_get_description(void);
esp_err_t custom_app_verify_firmware(const uint8_t* firmware_data, size_t size, const custom_app_desc_t* desc);
esp_err_t custom_app_verify_compatibility(const custom_app_desc_t* new_desc, const custom_app_desc_t* current_desc);
uint32_t custom_app_calculate_crc32(const void* data, size_t size);
bool custom_app_verify_signature(const uint8_t* data, size_t data_size, const uint8_t* signature, size_t sig_size);

#ifdef __cplusplus
}
#endif

#endif // _CUSTOM_APP_DESC_H
