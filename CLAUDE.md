# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

ESP32-S3 digital clock firmware using ESP-IDF v5.3.3 and ESP-ADF frameworks. Features I80 LCD display, WiFi, audio playback, speech recognition, RTC, environmental sensors, and OTA updates.

## Build Commands

### Environment Setup
```bash
# Source ESP-IDF (required)
. $IDF_PATH/export.sh  # Linux/macOS
# or
%IDF_PATH%\export.bat  # Windows

# Source ESP-ADF (required)
. $ADF_PATH/export.sh  # Linux/macOS
# or
%ADF_PATH%\export.bat  # Windows
```

### Build & Flash
```bash
idf.py build                    # Build project
idf.py flash                    # Flash firmware
idf.py monitor                  # Monitor serial output
idf.py menuconfig              # Configure project settings

# Alternative direct flashing
esptool.py -p /dev/ttyUSB0 -b 460800 write_flash 0x0 build/dd-clock.bin
```

### Development Commands
```bash
idf.py flash monitor          # Flash and monitor
idf.py menuconfig            # Project configuration
idf.py size                  # Show flash size info
idf.py partition-table       # Show partition table info
```

## Architecture

### Hardware Platform
- **Target**: ESP32-S3 with 16MB flash, 8MB PSRAM
- **Display**: 320x240 I80 LCD via LCD-I80 interface
- **Audio**: ES8388 codec with I2S 16-bit/44.1kHz
- **Sensors**: HTU21D (temp/humidity), battery voltage ADC
- **Inputs**: PCF8563 RTC, PCA9557 I/O expander, encoder, keys
- **Power**: Battery management with deep sleep modes

### Partition Layout
- Factory: 6MB
- OTA0: 6MB  
- OTA data: 8KB
- NVS: 16KB
- Storage (SPIFFS): 1MB
- Model (SPIFFS): 2MB

### Core Components

#### `/main/`
- **main.c**: Application entry, device initialization
- **periph_manager.c**: Peripheral management (SD, SPIFFS)
- **player_control.c**: Audio playback/recorder interface
- **timer_task.c**: Scheduled tasks and alarms
- **sys_sleep.c**: Power management and sleep modes

#### GUI System
- **gui_port/**: LVGL port for display driver
- **guider/**: Generated UI screens and navigation
- **generated/**: Auto-generated UI setup functions

#### Drivers (`/components/drv_*`)
- **drv_adc.c**: Battery voltage, analog mic readings
- **drv_rtc.c**: PCF8563 real-time clock driver
- **drv_htu21d.c**: Temperature/humidity sensor
- **drv_lcd_i80.c**: LCD parallel interface
- **drv_key.c**: Button/keypad driver

#### AI/Cloud Services
- **xzai/**: Speech recognition with wake word detection
- **qweather/**: Weather API integration
- **sntp_service/**: Network time synchronization

## Key Dependencies

- **ESP-IDF**: v5.3.3 (environment variables required)
- **ESP-ADF**: For audio processing
- **LVGL**: Graphics library for UI
- **esp_websocket_client**: WebSocket client support
- **esp_lcd_ili9341**: LCD driver

## Configuration Files

- **sdkconfig.defaults**: Default IDF configuration
- **partitions.csv**: Flash partition layout  
- **user_config.h**: User-specific settings
- **dev_config.h**: Runtime stored in NVS flash

## Development Notes

- **Build**: Uses CMake via idf.py toolchain
- **Components**: Modular design with separate driver/service components  
- **Storage**: SPIFFS for persistent data, models in dedicated partition
- **Power**: Deep sleep with RTC wake for battery operation
- **OTA**: Supports wireless updates via standard ESP-IDF mechanism