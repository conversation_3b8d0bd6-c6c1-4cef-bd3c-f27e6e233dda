set(SRC_DIR ${CMAKE_CURRENT_LIST_DIR})

####################################源文件########################################
#config
file(GLOB_RECURSE CONFIG_SOURCES ${SRC_DIR}/config/*.c)
list(APPEND ALL_SOURCE_FILES ${CONFIG_SOURCES})

#gui_port
file(GLOB_RECURSE GUI_PORT_SOURCES ${SRC_DIR}/gui_port/*.c)
list(APPEND ALL_SOURCE_FILES ${GUI_PORT_SOURCES})

#guider 
file(GLOB_RECURSE GUIDER_SOURCES ${SRC_DIR}/guider/*.c)
list(APPEND ALL_SOURCE_FILES ${GUIDER_SOURCES})

#misc
file(GLOB_RECURSE MISC_SOURCES ${SRC_DIR}/misc/*.c)
list(APPEND ALL_SOURCE_FILES ${MISC_SOURCES})

#./
file(GLOB_RECURSE ROOT_SOURCES ${SRC_DIR}/*.c)
list(APPEND ALL_SOURCE_FILES ${ROOT_SOURCES})

#排除不需要的源文件
file(GLOB_RECURSE REMOVE_SOURCES 
    ${SRC_DIR}/play_mp3_control_example.c
    )
list(REMOVE_ITEM ALL_SOURCE_FILES ${REMOVE_SOURCES})

foreach(file IN LISTS ALL_SOURCE_FILES)
    message("${file}")
endforeach()

set(SRC_SOURCES)
list(APPEND SRC_SOURCES ${ALL_SOURCE_FILES})

if(CONFIG_MY_BOARD_DDCLOCK)
    set(BOARD_TYPE "ddclock")
endif()

idf_component_register(SRCS 
                    ${SRC_SOURCES}
                    INCLUDE_DIRS 
                    ""
                    ${SRC_DIR}/include
                    ${SRC_DIR}/gui_port
                    ${SRC_DIR}/guider
                    ${SRC_DIR}/guider/custom
                    ${SRC_DIR}/guider/generated
                    ${SRC_DIR}/guider/images
                    ${SRC_DIR}/guider/misc
                    ${SRC_DIR}/guider/user
                    ${SRC_DIR}/misc
                    ${SRC_DIR}/config
                    ${SRC_DIR}/www
                    EMBED_FILES
                    ./www/index_wifi_config.html.gz
                    ./www/index_ota_update.html.gz)

if(NOT BOARD_NAME)
    set(BOARD_NAME ${BOARD_TYPE})
endif()
target_compile_definitions(${COMPONENT_LIB}
                            PRIVATE BOARD_TYPE=\"${BOARD_TYPE}\" BOARD_NAME=\"${BOARD_NAME}\"
                            )

# Create a SPIFFS image from the contents of the 'spiffs_image' directory
# that fits the partition named 'storage'. FLASH_IN_PROJECT indicates that
# the generated image should be flashed when the entire project is flashed to
# the target with 'idf.py -p PORT flash'.
spiffs_create_partition_image(storage ./spiffs_image FLASH_IN_PROJECT)

# 添加构建后处理脚本，用于更新自定义固件描述信息
add_custom_command(TARGET ${COMPONENT_LIB} POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E echo "Updating custom firmware description..."
    COMMAND python3 ${CMAKE_CURRENT_SOURCE_DIR}/scripts/update_custom_desc.py
            ${CMAKE_BINARY_DIR}/${CMAKE_PROJECT_NAME}.bin
            ${GIT_COMMIT_HASH}
            ${GIT_BRANCH}
            ${BUILD_TIMESTAMP}
            ${BUILD_NUMBER}
    COMMENT "Updating custom firmware description in binary"
    VERBATIM
)