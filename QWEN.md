# Project Context for DDClock

## Project Overview

This is a firmware project for a custom-designed digital clock device, likely based on the ESP32-S3 microcontroller. The project is built using the ESP-IDF (Espressif IoT Development Framework) and leverages the ADF (Audio Development Framework). The clock features include:

*   **I80 LCD Display:** A parallel interface LCD for the user interface.
*   **WiFi Connectivity:** For network time synchronization (SNTP) and potentially other network services.
*   **Audio Playback:** Using an audio codec, managed by the ADF, to play sounds (e.g., alarms, notifications).
*   **Speech Recognition (AI Chat):** Integration with Espressif's speech recognition libraries, including wake word detection and potentially cloud-based command processing via MQTT.
*   **RTC (Real-Time Clock):** A hardware RTC for timekeeping, likely with backup battery support.
*   **Environmental Sensors:** Integration with an HTU21D sensor for temperature and humidity monitoring.
*   **Battery Management:** Monitoring battery voltage and managing power states, including deep sleep.
*   **Alarm Clock Functionality:** Multiple configurable alarms.
*   **OTA (Over-The-Air) Updates:** Support for updating the firmware wirelessly.
*   **PC Monitor Client:** A feature to monitor a PC's performance, likely via a dedicated server.
*   **Touch or Button-based UI:** Interaction with the device, managed by a GUI system (possibly LVGL, inferred from `gui_port` and `lv_conf.h` in docs).
*   **Peripherals:** Management of various hardware peripherals like I2C devices (PCA9557 I/O expander, HTU21D, RTC), ADC for battery sensing, and a beeper.

The codebase is modular, with core application logic in `main/` and peripheral drivers, utilities, and libraries in `components/`.

## Building and Running

*   **Framework:** ESP-IDF (v5.3.3 inferred from sdkconfig) and ESP-ADF.
*   **Target Chip:** ESP32-S3 (CONFIG_IDF_TARGET_ESP32S3).
*   **Build System:** CMake (via `CMakeLists.txt`) and potentially legacy Make (`Makefile` present).
*   **Dependencies:**
    *   ESP-IDF: Environment variable `IDF_PATH` must be set.
    *   ESP-ADF: Environment variable `ADF_PATH` must be set.
    *   Component dependencies are likely managed via `idf_component.yml` files within `components/` and `main/`.
*   **Partition Table:** Custom partition table defined in `partitions.csv`. Includes partitions for NVS, OTA data, PHY init, Factory app, OTA app slot, and SPIFFS partitions for storage and potentially speech recognition models.
*   **Flash Size:** Configured for 16MB flash (CONFIG_ESPTOOLPY_FLASHSIZE_16MB).
*   **Build Commands (Typical IDF/ADF flow):**
    1.  Source the ESP-IDF environment: `. $IDF_PATH/export.sh` (Linux/macOS) or `%IDF_PATH%\export.bat` (Windows).
    2.  Configure the project (if needed): `idf.py menuconfig`. Note the project-specific settings like WiFi credentials, weather API keys, and MQTT settings are managed via `dev_config` in NVS.
    3.  Build the project: `idf.py build`.
*   **Flash Command (Typical IDF/ADF flow):**
    *   Flash the built binaries: `idf.py flash`. This uses settings from `sdkconfig` and `partitions.csv`.
*   **Monitor Command (Typical IDF/ADF flow):**
    *   View serial output: `idf.py monitor`.

*TODO: Specific build/run instructions should be confirmed by checking `scripts/` directory or project documentation if a README existed.*

## Development Conventions

*   **Language:** C (Primary), C++ (Possibly for LVGL or other components).
*   **Modularity:** Code is organized into components (in `components/`) and main application code (in `main/`). Each component typically has its own `CMakeLists.txt` or `component.mk`.
*   **Hardware Abstraction:** Peripheral drivers (`drv_*`) and Hardware Abstraction Layer services (`hal_*`) are used to interface with hardware components, promoting modularity.
*   **Configuration:** System-wide settings are managed via Kconfig/Kconfig.projbuild files (not directly visible here) which generate the `sdkconfig` file. Application-specific user settings (like WiFi SSID, alarm times) are stored in NVS and accessed via `dev_config`.
*   **File System:** SPIFFS is used for storage (`storage` partition) and potentially for speech recognition models (`model` partition).
*   **Power Management:** Deep sleep and wakeup mechanisms are implemented, managed by `sys_sleep.c` and related functions in `main.c`.
*   **Logging:** ESP-IDF's logging library (`ESP_LOGx`) is used for debug output.
*   **Peripherals:** A `periph_manager` and `hal_service` framework seems to be used to manage various hardware services and tasks.
*   **GUI:** UI logic is handled by files in `main/gui_port` and `main/guider`, likely interfacing with a library like LVGL (suggested by `lv_conf.h` in `doc/`).